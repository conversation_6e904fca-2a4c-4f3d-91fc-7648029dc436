import yaml
import os


def read_yaml(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            data = yaml.safe_load(file)
        return data
    except FileNotFoundError:
        return None
    except Exception as e:
        return None


def write_yaml(data, file_path):
    try:
        with open(file_path, 'w', encoding='utf-8') as file:
            # 使用自定义的YAML格式化来保持原始格式
            yaml_content = format_yaml_content(data)
            file.write(yaml_content)
        return True
    except Exception as e:
        return False


def format_yaml_content(data):
    """
    自定义格式化YAML内容，保持原始格式
    """
    content = []
    content.append(f"image_width: {data['image_width']}")
    content.append(f"image_height: {data['image_height']}")
    content.append(f"camera_name: {data['camera_name']}")

    # 格式化camera_matrix
    content.append("camera_matrix:")
    content.append(f"  rows: {data['camera_matrix']['rows']}")
    content.append(f"  cols: {data['camera_matrix']['cols']}")

    # 格式化camera_matrix的data为3x4矩阵格式
    matrix_data = data['camera_matrix']['data']
    content.append("  data: [ {:>2}, {:>7}, {:>7}, {:>3},".format(
        format_number(matrix_data[0]),
        format_number(matrix_data[1]),
        format_number(matrix_data[2]),
        format_number(matrix_data[3])
    ))
    content.append("         {:>3} {:>7}, {:>3}, {:>3},".format(
        format_number(matrix_data[4]),
        format_number(matrix_data[5]),
        format_number(matrix_data[6]),
        format_number(matrix_data[7])
    ))
    content.append("         {:>3} {:>7}, {:>7}, {:>3} ]".format(
        format_number(matrix_data[8]),
        format_number(matrix_data[9]),
        format_number(matrix_data[10]),
        format_number(matrix_data[11])
    ))

    content.append(f"camera_model: {data['camera_model']}")
    content.append(f"distortion_model: {data['distortion_model']}")

    # 格式化distortion_coefficients
    content.append("distortion_coefficients:")
    content.append(f"  rows: {data['distortion_coefficients']['rows']}")
    content.append(f"  cols: {data['distortion_coefficients']['cols']}")
    dist_data = data['distortion_coefficients']['data']
    content.append("  data: [{}]".format(", ".join([str(x) for x in dist_data])))

    # 格式化rectification_matrix
    content.append("rectification_matrix:")
    content.append(f"  rows: {data['rectification_matrix']['rows']}")
    content.append(f"  cols: {data['rectification_matrix']['cols']}")
    rect_data = data['rectification_matrix']['data']
    content.append("  data: [{}, {}, {},".format(rect_data[0], rect_data[1], rect_data[2]))
    content.append("         {}, {}, {},".format(rect_data[3], rect_data[4], rect_data[5]))
    content.append("         {}, {}, {}]".format(rect_data[6], rect_data[7], rect_data[8]))

    # 格式化projection_matrix
    content.append("projection_matrix:")
    content.append(f"  rows: {data['projection_matrix']['rows']}")
    content.append(f"  cols: {data['projection_matrix']['cols']}")
    proj_data = data['projection_matrix']['data']
    content.append("  data: [{:>9}, {:>9}, {:>9}, {:>9},".format(
        format_number(proj_data[0]),
        format_number(proj_data[1]),
        format_number(proj_data[2]),
        format_number(proj_data[3])
    ))
    content.append("         {:>9}, {:>9}, {:>9}, {:>9},".format(
        format_number(proj_data[4]),
        format_number(proj_data[5]),
        format_number(proj_data[6]),
        format_number(proj_data[7])
    ))
    content.append("         {:>9}, {:>9}, {:>9}, {:>9}]".format(
        format_number(proj_data[8]),
        format_number(proj_data[9]),
        format_number(proj_data[10]),
        format_number(proj_data[11])
    ))

    content.append("")  # 添加空行

    return "\n".join(content)


def format_number(num):
    """
    格式化数字，保持原始格式
    """
    if num == 0:
        return "0."
    elif num == 1:
        return "1."
    elif num == -1:
        return "-1"
    elif isinstance(num, float):
        if num.is_integer():
            return f"{int(num)}."
        else:
            return f"{num}"
    else:
        return str(num)


def read_and_analysis():
    """
    读取并分析YAML文件，修改camera_matrix中的data数据
    """
    # 输入文件路径
    input_file = "实践1/数据/camera.yaml"
    # 输出文件路径
    output_file = "实践1/实验2/camera2.yaml"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 {input_file}")
        return
    
    # 读取YAML文件
    print("正在读取camera.yaml文件...")
    data = read_yaml(input_file)
    
    if data is None:
        print("读取文件失败")
        return
    
    print("原始camera_matrix数据:")
    print(data['camera_matrix']['data'])
    
    # 修改camera_matrix中的data数据
    # 根据您提供的修改后的数据（3x4矩阵）
    new_data = [-1, 0., 0, -1,
                0., 0, -1, -1,
                0., 0., 0., -1.]
    
    data['camera_matrix']['data'] = new_data
    
    print("修改后camera_matrix数据:")
    print(data['camera_matrix']['data'])
    
    # 写入新的YAML文件
    print("正在写入camera2.yaml文件...")
    success = write_yaml(data, output_file)
    
    if success:
        print("修改完成！")
    else:
        print("修改失败！")


def main():
    """
    主函数
    """
    print("=" * 50)
    print("实验2-YAML/JSON配置文件读写")
    print("=" * 50)
    
    read_and_analysis()


if __name__ == "__main__":
    main()
