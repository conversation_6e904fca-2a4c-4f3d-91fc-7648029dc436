import yaml
import os


def read_yaml(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            data = yaml.safe_load(file)
        return data
    except FileNotFoundError:
        return None
    except Exception as e:
        return None


def write_yaml(data, file_path):
    """
    写入YAML文件
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as file:
            yaml.dump(data, file, default_flow_style=False, allow_unicode=True)
        print(f"文件已保存到: {file_path}")
        return True
    except Exception as e:
        print(f"写入文件时发生错误: {e}")
        return False


def read_and_analysis():
    """
    读取并分析YAML文件，修改camera_matrix中的data数据
    """
    # 输入文件路径
    input_file = "实践1/数据/camera.yaml"
    # 输出文件路径
    output_file = "实践1/实验2/camera2.yaml"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 {input_file}")
        return
    
    # 读取YAML文件
    print("正在读取camera.yaml文件...")
    data = read_yaml(input_file)
    
    if data is None:
        print("读取文件失败")
        return
    
    print("原始camera_matrix数据:")
    print(data['camera_matrix']['data'])
    
    # 修改camera_matrix中的data数据
    # 根据您提供的修改后的数据
    new_data = [-1, 0., 0, -1,
                0., 0, -1, -1,
                0., 0., 0., -1.]
    
    data['camera_matrix']['data'] = new_data
    
    print("修改后camera_matrix数据:")
    print(data['camera_matrix']['data'])
    
    # 写入新的YAML文件
    print("正在写入camera2.yaml文件...")
    success = write_yaml(data, output_file)
    
    if success:
        print("修改完成！")
    else:
        print("修改失败！")


def main():
    """
    主函数
    """
    print("=" * 50)
    print("实验2-YAML/JSON配置文件读写")
    print("=" * 50)
    
    read_and_analysis()


if __name__ == "__main__":
    main()
