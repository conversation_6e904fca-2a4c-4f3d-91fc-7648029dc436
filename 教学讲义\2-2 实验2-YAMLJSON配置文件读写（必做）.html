<html>
<head>
  <title>2-2 实验2-YAML/JSON配置文件读写（必做）</title>
  <basefont face="仿宋" size="2" />
  <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
  <meta name="exporter-version" content="YXBJ Windows/607711 (zh-CN, DDL); Windows/10.0.0 (Win64); EDAMVersion=V2;"/>
  <style>
    body, td {
      font-family: 仿宋;
      font-size: 14pt;
    }
  </style>
</head>
<body>
<a name="6455"/>
<h1>2-2 实验2-YAML/JSON配置文件读写（必做）</h1>

<div>
<span><div><div><span style="font-weight: bold;">2-2 实验2-YAML/JSON配置文件读写（必做）</span></div><div><span style="color: rgb(105, 170, 53);">▶</span> <a href="0程序设计I-Python版- 实践1.html" style="color: rgb(105, 170, 53);">0程序设计I-Python版- 实践1</a></div><div><br/></div><div><span style="font-size: 14pt; color: rgb(54, 101, 238); font-weight: bold;">目的</span></div><ul><li><div>了解在编程中引入第三方库并使用的方法。</div></li><li><div>掌握基本数据类型和基本程序控制结构 的使用。</div></li><li><div>掌握基本的文件操作。</div></li></ul><div><br/></div><div><span style="font-size: 14pt; color: rgb(54, 101, 238); font-weight: bold;">课程实践数据（学习平台/实践1）</span></div><ul><li><div>实践1\数据\camera.yaml</div></li></ul><div><br/></div><div><span style="font-size: 14pt; color: rgb(54, 101, 238); font-weight: bold;">实现步骤</span><span style="font-size: 14pt; color: rgb(255, 0, 0); font-weight: bold;">（参考录课视频复现以下操作）</span></div><ul><li><div>建立YamlTest.py文件。</div></li><li><div>定义read_and_analysis方法，实现以下功能：</div></li><ul><li><div>定义read_yaml方法，读取camera.yaml文件。</div></li><li><div>修改camera_matrix中的data数据，修改效果如下：</div></li><li><div>修改前：</div></li><li><div><img src="2-2 实验2-YAMLJSON配置文件读写（必做）_files/Image.png" type="image/png" data-filename="Image.png"/></div></li><li><div>修改后：</div></li><li><div><img src="2-2 实验2-YAMLJSON配置文件读写（必做）_files/Image [1].png" type="image/png" data-filename="Image.png"/></div></li><li><div>定义write_yaml方法，将修改写入camera2.yaml文件。</div></li></ul></ul><div><br/></div><div><span style="font-size: 14pt; color: rgb(54, 101, 238); font-weight: bold;">课程实践提交内容（</span><span style="font-size: 14pt; color: rgb(255, 0, 0); font-weight: bold;">等所有实验完成后，</span><span style="color: rgb(255, 0, 0); font-weight: bold;">实践1</span><span style="font-size: 14pt; color: rgb(255, 0, 0); font-weight: bold;">文件</span><span style="font-size: 14pt; color: rgb(227, 0, 0); font-weight: bold;">夹</span><span style="font-size: 14pt; color: rgb(255, 0, 0); font-weight: bold;">打包后提交</span><span style="font-size: 14pt; color: rgb(54, 101, 238); font-weight: bold;">）</span></div><ul><li><div>建立<span style="color: rgb(255, 0, 0); font-weight: bold;">实践1/实验2</span>文件夹</div></li><li><div>放入程序和结果文件：</div></li><ul><li><div><span style="color: rgb(255, 0, 0); font-weight: bold;">实践1</span><span style="color: rgb(255, 0, 0); font-weight: bold;">/实验2</span>/YamlTest.py</div></li><li><div><span style="color: rgb(255, 0, 0); font-weight: bold;">实践1</span><span style="color: rgb(255, 0, 0); font-weight: bold;">/实验2</span>/camera2.yaml</div></li></ul></ul><div><br/></div><hr/></div><div><span style="color: rgb(105, 170, 53);">▶</span> <a href="0程序设计I-Python版- 实践1.html" style="color: rgb(105, 170, 53);">0程序设计I-Python版- 实践1</a></div></span>
</div></body></html> 