<html>
<head>
  <title>2-1 实验1-基于情感分析库SNOWNLP分析情感（必做）</title>
  <basefont face="仿宋" size="2" />
  <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
  <meta name="exporter-version" content="YXBJ Windows/607711 (zh-CN, DDL); Windows/10.0.0 (Win64); EDAMVersion=V2;"/>
  <style>
    body, td {
      font-family: 仿宋;
      font-size: 14pt;
    }
  </style>
</head>
<body>
<a name="6451"/>
<h1>2-1 实验1-基于情感分析库SNOWNLP分析情感（必做）</h1>

<div>
<span><div><div><span style="font-size: 18pt;"><span style="font-size: 18pt; color: rgb(54, 101, 238); font-weight: bold;">2-1 实验1-基于情感分析库SNOWNLP分析情感（必做）</span></span></div><div><span style="color: rgb(105, 170, 53);">▶</span> <a href="0程序设计I-Python版- 实践1.html" style="color: rgb(105, 170, 53);">0程序设计I-Python版- 实践1</a></div><div><br/></div><div><br/></div><div><span style="font-size: 14pt; color: rgb(54, 101, 238); font-weight: bold;">目的</span></div><ul><li><div>了解在编程中引入第三方库并使用的方法。</div></li><li><div>掌握基本数据类型和基本程序控制结构 的使用。</div></li><li><div>掌握基本的文件操作。</div></li></ul><div><br/></div><div><span style="font-size: 14pt; color: rgb(54, 101, 238); font-weight: bold;">课程实践数据（学习平台/实践1）</span></div><ul><li><div>实践1\数据\Good.txt</div></li><li><div>实践1\数据\Bad.txt</div></li></ul><div><br/></div><div><span style="font-size: 14pt; color: rgb(54, 101, 238); font-weight: bold;">实现步骤</span><span style="font-size: 14pt;"><span style="font-size: 14pt; color: rgb(255, 0, 0); font-weight: bold;">（参考录课视频复现以下操作）</span></span></div><ul><li><div>建立SnownlpDemo.py文件。</div></li><li><div>定义read_and_analysis方法，实现以下功能：</div></li><ul><li><div>分别读取数据文件（见实践课程数据中的Good.txt, Bad.txt）。</div></li><li><div>使用SNOWNLP库分析，并在每一行所分析数据前加上SNOWNLP分析结果。</div></li><li><div>统计计算SNOWNLP库分析结果的平均值。</div></li><li><div>将每行的SNOWNLP分析结果和统计计算的平均值分别输出至EstimationGood.txt和EstimationBad.txt</div></li></ul><li><div>EstimationGood.txt和EstimationBad.txt中的数据样式：</div></li></ul><div><img src="2-1 实验1-基于情感分析库SNOWNLP分析情感（必做）_files/Image.png" type="image/png" data-filename="Image.png"/></div><div><br/></div><div><span style="font-size: 14pt; color: rgb(54, 101, 238); font-weight: bold;">课程实践提交内容（</span><span style="font-size: 14pt;"><span style="font-size: 14pt; color: rgb(255, 0, 0); font-weight: bold;">等所有实验完成后，</span></span><span style="color: rgb(255, 0, 0); font-weight: bold;">实践1</span><span style="font-size: 14pt;"><span style="font-size: 14pt;"><span style="font-size: 14pt; font-weight: bold; color: rgb(255, 0, 0);">文件</span></span><span style="font-size: 14pt; color: rgb(227, 0, 0); font-weight: bold;">夹</span></span><span style="font-size: 14pt; color: rgb(255, 0, 0); font-weight: bold;">打包后提交</span><span style="font-size: 14pt; color: rgb(54, 101, 238); font-weight: bold;">）</span></div><ul><li><div>建立<span style="font-weight: bold; color: rgb(255, 0, 0);">实践1/实验1</span>文件夹</div></li><li><div>放入程序和结果文件：</div></li><ul><li><div><span style="color: rgb(255, 0, 0); font-weight: bold;">实践1</span><span style="color: rgb(255, 0, 0); font-weight: bold;">/实验1</span>/SnownlpDemo.py</div></li><li><div><span style="color: rgb(255, 0, 0); font-weight: bold;">实践1</span><span style="color: rgb(255, 0, 0); font-weight: bold;">/实验1</span>/EstimationGood.txt</div></li><li><div><span style="color: rgb(255, 0, 0); font-weight: bold;">实践1</span><span style="color: rgb(255, 0, 0); font-weight: bold;">/实验1</span>/EstimationBad.txt</div></li></ul></ul><div><br/></div><div><br/></div><div><br/></div><hr/></div><div><span style="color: rgb(105, 170, 53);">▶</span> <a href="0程序设计I-Python版- 实践1.html" style="color: rgb(105, 170, 53);">0程序设计I-Python版- 实践1</a></div><div><br/></div><div><br/></div></span>
</div></body></html> 