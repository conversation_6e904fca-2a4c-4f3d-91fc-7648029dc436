<html>
<head>
  <title>1-1 程序错误</title>
  <basefont face="仿宋" size="2" />
  <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
  <meta name="exporter-version" content="YXBJ Windows/607711 (zh-CN, DDL); Windows/10.0.0 (Win64); EDAMVersion=V2;"/>
  <style>
    body, td {
      font-family: 仿宋;
      font-size: 14pt;
    }
  </style>
</head>
<body>
<a name="6339"/>
<h1>1-1 程序错误</h1>

<div>
<span><div><div><div><span style="font-size: 18pt; color: rgb(54, 101, 238); font-weight: bold;">1-1 程序错误</span></div><div><span style="color: rgb(105, 170, 53);">▶</span> <a href="0程序设计I-Python版- 实践1.html" style="color: rgb(105, 170, 53);">0程序设计I-Python版- 实践1</a></div><table style="border-collapse: collapse; min-width: 100%;"><colgroup><col style="width: 245px;"></col><col style="width: 673px;"></col></colgroup><tbody><tr><td style="background-color: rgb(0, 168, 45); border: 1px solid rgb(0, 134, 36); width: 245px; padding: 8px;"><div style="text-align: center;"><span style="font-size: 14pt; color: rgb(255, 255, 255); font-weight: bold;">线索/标题</span></div></td><td style="background-color: rgb(0, 168, 45); border: 1px solid rgb(0, 134, 36); width: 673px; padding: 8px;"><div style="text-align: center;"><font style="font-size: 14pt;"><span style="font-size: 14pt; color: rgb(255, 255, 255); font-weight: bold;">标题：</span><span style="font-size: 14pt; color: rgb(255, 255, 255); font-weight: bold;">程序错误</span></font></div></td></tr><tr><td rowspan="3" style="height: 92px; background-color: rgb(234, 234, 234); border: 1px solid rgb(187, 187, 187); width: 245px; padding: 8px;"><ul><li><div><span style="font-size: 14pt;">程序错误类型</span></div></li></ul><div><font style="font-size: 14pt;"><br/></font></div><div><font style="font-size: 14pt;"><br/></font></div></td><td style="border: 1px solid rgb(204, 204, 204); width: 673px; padding: 8px;"><ul><li><div><span style="font-size: 14pt;">语法错误</span></div></li><ul><li><div><span style="font-size: 14pt;">最容易处理的错误类型，可通过错误列表检查。</span></div></li></ul></ul><div><span style="font-size: 14pt;"><img src="1-1 程序错误_files/Image.png" type="image/png" data-filename="Image.png"/></span></div></td></tr><tr><td style="border: 1px solid rgb(204, 204, 204); width: 673px; padding: 8px;"><ul><li><div><span style="font-size: 14pt;">运行时错误</span></div></li><ul><li><div><span style="font-size: 14pt;">运行时产生的异常。</span></div></li><li><div><span style="font-size: 14pt;">一般在程序设计时需要考虑可能发生错误的地方，利用try-except-finally捕获错误类型进行相应的处理，但也只能处理一部分。</span></div></li><li><div><span style="font-size: 14pt;">通过问题复现和人工逻辑检查处理。</span></div></li></ul></ul></td></tr><tr><td style="border: 1px solid rgb(204, 204, 204); width: 673px; padding: 8px;"><ul><li><div><span style="font-size: 14pt;">逻辑错误</span></div></li><ul><li><div><span style="font-size: 14pt;">主要指运行结果与预期不符，一般是算法逻辑不严谨或逻辑错误导致。</span></div></li><li><div><span style="font-size: 14pt;">通过问题复现和人工逻辑检查处理。</span></div></li></ul></ul></td></tr><tr><td style="background-color: rgb(234, 234, 234); border: 1px solid rgb(187, 187, 187); width: 245px; padding: 8px;"><div><font style="font-size: 14pt;"><br/></font></div></td><td style="border: 1px solid rgb(204, 204, 204); width: 673px; padding: 8px;"><ul><li><div><span style="font-size: 14pt;">常见的复杂程序错误</span></div></li><ul><li><div><span style="font-size: 14pt;">内存泄漏</span></div></li><li><div><span style="font-size: 14pt;">内存溢出</span></div></li><li><div><span style="font-size: 14pt;">多线程交互程序</span></div></li><li><div><span style="font-size: 14pt;">运行时产生无效/不合规数据</span></div></li></ul></ul></td></tr><tr><td style="background-color: rgb(234, 234, 234); border: 1px solid rgb(187, 187, 187); width: 245px; padding: 8px;"><div><span style="font-size: 14pt; font-weight: bold;">标签：</span></div><div><font style="font-size: 14pt;"><br/></font></div><div><font style="font-size: 14pt;"><br/></font></div><div><font style="font-size: 14pt;"><br/></font></div></td><td style="background-color: rgb(234, 234, 234); border: 1px solid rgb(187, 187, 187); width: 673px; padding: 8px;"><div><span style="font-size: 14pt; font-weight: bold;">总结：</span></div><div><font style="font-size: 14pt;"><br/></font></div><div><font style="font-size: 14pt;"><br/></font></div><div><font style="font-size: 14pt;"><br/></font></div></td></tr></tbody></table><div><font style="font-size: 14pt;"><br/></font></div></div><hr/><div><span style="color: rgb(105, 170, 53);">▶</span> <a href="0程序设计I-Python版- 实践1.html" style="color: rgb(105, 170, 53);">0程序设计I-Python版- 实践1</a></div></div><div><br/></div></span>
</div></body></html> 