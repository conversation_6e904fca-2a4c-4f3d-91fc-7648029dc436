import snownlp
import os
def read_and_analysis(input_file, output_file):

    try:
        # 读取输入文件
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 存储分析结果
        analysis_results = []
        sentiment_scores = []
        
        print(f"正在分析文件: {input_file}")
        
        # 对每一行进行情感分析
        for i, line in enumerate(lines, 1):
            line = line.strip()  # 去除换行符和空格
            if line:  # 如果行不为空
                # 使用SNOWNLP进行情感分析
                s = snownlp.SnowNLP(line)
                sentiment_score = s.sentiments
                sentiment_scores.append(sentiment_score)
                
                # 格式化输出：[情感分析结果] + 原文本
                result_line = f"[{sentiment_score:.15f}]: {line}"
                analysis_results.append(result_line)
                
                print(f"第{i}行分析完成，情感分数: {sentiment_score:.6f}")
        
        # 计算平均值
        if sentiment_scores:
            average_sentiment = sum(sentiment_scores) / len(sentiment_scores)
            print(f"情感分析平均值: {average_sentiment:.6f}")
            
            # 将结果写入输出文件
            with open(output_file, 'w', encoding='utf-8') as f:
                # 写入每行的分析结果
                for result in analysis_results:
                    f.write(result + '\n')
                
                # 写入平均值（带星号格式）
                f.write(f"\n***********SCORE:{average_sentiment:.15f}\n")
            
            print(f"分析结果已保存到: {output_file}")
            return average_sentiment
        else:
            print("文件为空或没有有效内容")
            return 0
            
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
        return 0
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        return 0


def main():

    good_input = "实践1/数据/Good.txt"
    bad_input = "实践1/数据/Bad.txt"
    
    # 输出文件路径
    good_output = "EstimationGood.txt"
    bad_output = "EstimationBad.txt"
    
    # 检查输入文件是否存在
    if not os.path.exists(good_input):
        print(f"错误: 找不到文件 {good_input}")
        print("请确保数据文件在正确的位置")
        return
    
    if not os.path.exists(bad_input):
        print(f"错误: 找不到文件 {bad_input}")
        print("请确保数据文件在正确的位置")
        return
    
    # 分析Good.txt文件
    print("\n开始分析Good.txt文件...")
    good_avg = read_and_analysis(good_input, good_output)
    
    print("\n" + "-" * 30)
    
    # 分析Bad.txt文件
    print("\n开始分析Bad.txt文件...")
    bad_avg = read_and_analysis(bad_input, bad_output)
    
    # 输出总结

    print(f"Good.txt 情感分析平均值: {good_avg:.6f}")
    print(f"Bad.txt 情感分析平均值: {bad_avg:.6f}")
    print("=" * 50)
    
    # 简单的情感倾向判断
    print("\n情感倾向分析:")
    if good_avg > 0.5:
        print("Good.txt: 整体情感倾向为正面")
    else:
        print("Good.txt: 整体情感倾向为负面")
        
    if bad_avg > 0.5:
        print("Bad.txt: 整体情感倾向为正面")
    else:
        print("Bad.txt: 整体情感倾向为负面")


if __name__ == "__main__":
    main()
