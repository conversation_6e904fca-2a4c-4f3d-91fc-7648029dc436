import snownlp
import os
def read_and_analysis(input_file, output_file):

    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        analysis_results = []
        sentiment_scores = []

        for line in lines:
            line = line.strip()
            if line:
                s = snownlp.SnowNLP(line)
                sentiment_score = s.sentiments
                sentiment_scores.append(sentiment_score)

                result_line = f"[{sentiment_score:.15f}]: {line}"
                analysis_results.append(result_line)

        if sentiment_scores:
            average_sentiment = sum(sentiment_scores) / len(sentiment_scores)

            with open(output_file, 'w', encoding='utf-8') as f:
                for result in analysis_results:
                    f.write(result + '\n')

                f.write(f"\n***********SCORE:{average_sentiment:.15f}\n")

            return average_sentiment
        else:
            return 0
            
    except FileNotFoundError:
        return 0
    except Exception as e:
        return 0


def main():

    good_input = "实践1/数据/Good.txt"
    bad_input = "实践1/数据/Bad.txt"

    good_output = "EstimationGood.txt"
    bad_output = "EstimationBad.txt"

    if not os.path.exists(good_input):
        return

    if not os.path.exists(bad_input):
        return

    read_and_analysis(good_input, good_output)
    read_and_analysis(bad_input, bad_output)
    print("Done!")


if __name__ == "__main__":
    main()
