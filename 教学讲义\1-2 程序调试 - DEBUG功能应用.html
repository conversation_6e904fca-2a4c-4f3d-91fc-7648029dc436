<html>
<head>
  <title>1-2 程序调试 - DEBUG功能应用</title>
  <basefont face="仿宋" size="2" />
  <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
  <meta name="exporter-version" content="YXBJ Windows/607711 (zh-CN, DDL); Windows/10.0.0 (Win64); EDAMVersion=V2;"/>
  <style>
    body, td {
      font-family: 仿宋;
      font-size: 14pt;
    }
  </style>
</head>
<body>
<a name="6341"/>
<h1>1-2 程序调试 - DEBUG功能应用</h1>

<div>
<span><div><div><font style="font-size: 18pt;"><span style="letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; font-size: 18pt; color: rgb(54, 101, 238); font-variant-caps: normal; font-variant-ligatures: normal; font-weight: bold;">1-2 </span><span style="font-size: 18pt; color: rgb(54, 101, 238); font-weight: bold;">程序调试 - DEBUG功能应用</span></font></div><div><span style="color: rgb(105, 170, 53);">▶</span> <a href="0程序设计I-Python版- 实践1.html" style="color: rgb(105, 170, 53);">0程序设计I-Python版- 实践1</a></div><div><font style="font-size: 14pt;"><br/></font></div><div><span style="font-size: 14pt; color: rgb(54, 101, 238); font-weight: bold;">调使用DEMO程序，冒泡排序</span></div><ul><li><div><span style="font-size: 14pt;">原理：</span><span style="font-size: 14pt; color: unset;">假设有一个大小为 N 的无序序列。冒泡排序就是要每趟排序过程中通过两两比较，找到第 i 个小（大）的元素，将其往上排。</span></div></li></ul><div style="text-align: left;"><span style="font-size: 14pt;"><img src="1-2 程序调试 - DEBUG功能应用_files/Image.png" type="image/png" data-filename="Image.png"/></span></div><div><font style="font-size: 14pt;"><br/></font></div><div><font style="font-size: 14pt;"><br/></font></div><div style="box-sizing: border-box; padding: 8px; font-family: Monaco, Menlo, Consolas, &quot;Courier New&quot;, monospace; font-size: 12px; color: rgb(51, 51, 51); border-radius: 4px; background-color: rgb(251, 250, 248); border: 1px solid rgba(0, 0, 0, 0.15);-en-codeblock:true;"><div><font face="仿宋" style="font-size: 14pt;">import random</font></div><div><font face="仿宋" style="font-size: 14pt;"><br/></font></div><div><font face="仿宋" style="font-size: 14pt;">arr=[]</font></div><div><font face="仿宋" style="font-size: 14pt;"><br/></font></div><div><font face="仿宋" style="font-size: 14pt;">def produceData(number):</font></div><div><font face="仿宋" style="font-size: 14pt;">    for var in range(number):</font></div><div><font face="仿宋" style="font-size: 14pt;">        arr.append(random.randint(1, 100))</font></div><div><font face="仿宋" style="font-size: 14pt;"><br/></font></div><div><font face="仿宋" style="font-size: 14pt;">def changeValue(j):</font></div><div><font face="仿宋" style="font-size: 14pt;">    arr[j], arr[j + 1] = arr[j + 1], arr[j]</font></div><div><font face="仿宋" style="font-size: 14pt;"><br/></font></div><div><font face="仿宋" style="font-size: 14pt;">def bubbleSort(arr):</font></div><div><font face="仿宋" style="font-size: 14pt;">    n = len(arr)</font></div><div><font face="仿宋" style="font-size: 14pt;">    # 遍历所有数组元素（冒泡排序算法）</font></div><div><font face="仿宋" style="font-size: 14pt;">    for i in range(n):</font></div><div><font face="仿宋" style="font-size: 14pt;">        # Last i elements are already in place</font></div><div><font face="仿宋" style="font-size: 14pt;">        for j in range(0, n - i - 1):</font></div><div><font face="仿宋" style="font-size: 14pt;">            if arr[j] &gt; arr[j + 1]:</font></div><div><font face="仿宋" style="font-size: 14pt;">                changeValue(j)</font></div><div><font face="仿宋" style="font-size: 14pt;"><br/></font></div><div><br/></div><div><font face="仿宋" style="font-size: 14pt;">if __name__ == '__main__':</font></div><div><font style="font-size: 14pt;"><font face="仿宋">   <font> </font></font><font face="仿宋"># 测序序列</font></font></div><div><font face="仿宋" style="font-size: 14pt;">    produceData(15)</font></div><div><font face="仿宋" style="font-size: 14pt;">    print(&quot;排序前的数组:&quot;)</font></div><div><font face="仿宋" style="font-size: 14pt;">    for i in range(len(arr)):</font></div><div><font face="仿宋" style="font-size: 14pt;">        print(&quot;[%d]: %d&quot; % (i, arr[i]), end=&quot;, &quot;)</font></div><div><font face="仿宋" style="font-size: 14pt;">    </font></div><div><font face="仿宋" style="font-size: 14pt;">     # 排序</font></div><div><font face="仿宋" style="font-size: 14pt;">    bubbleSort(arr)</font></div><div><font face="仿宋" style="font-size: 14pt;">    </font></div><div><font face="仿宋" style="font-size: 14pt;">    # 打印结果</font></div><div><font face="仿宋" style="font-size: 14pt;">    print(&quot;&quot;)</font></div><div><font face="仿宋" style="font-size: 14pt;">    print(&quot;排序后的数组:&quot;)</font></div><div><font face="仿宋" style="font-size: 14pt;">    for i in range(len(arr)):</font></div><div><font face="仿宋" style="font-size: 14pt;">        print(&quot;[%d]: %d&quot; % (i, arr[i]), end=&quot;, &quot;)</font></div><div><br/></div></div><div><font style="font-size: 14pt;"><br/></font></div><div><font style="font-size: 14pt;"><br/></font></div><hr/><div><span style="font-size: 14pt; color: rgb(54, 101, 238); font-weight: bold;">调试步骤</span></div><ul><li><div><span style="font-size: 14pt;">设置程序断点</span></div></li><ul><li><div><span style="font-size: 14pt;">在行号和代码之间的空白处单击鼠标左键，出现红色的小圆点即为断点。</span></div></li></ul></ul><div><span style="font-size: 14pt;"><img src="1-2 程序调试 - DEBUG功能应用_files/Image [1].png" type="image/png" data-filename="Image.png"/></span></div><ul><li><div><span style="font-size: 14pt;">选择Run-&gt;Debug XXX模式，进入Debug调试流程，此时程序会运行到断点处，注意蓝色条纹即为程序运行的当前位置。</span></div></li></ul><div><span style="font-size: 14pt;"><img src="1-2 程序调试 - DEBUG功能应用_files/Image [2].png" type="image/png" data-filename="Image.png"/></span></div><ul><li><div><span style="font-size: 14pt;">调试按钮</span></div></li></ul><div><span style="font-size: 14pt;"><img src="1-2 程序调试 - DEBUG功能应用_files/Image [3].png" type="image/png" data-filename="Image.png"/></span></div><ul><ul><li><div><span style="font-size: 14pt;">左1：Step Over</span></div></li><ul><li><div><span style="font-size: 14pt;"> 逐行执行，碰到函数不进入。</span></div></li></ul><li><div><span style="font-size: 14pt;">左2：Step Into</span></div></li><ul><li><div><span style="font-size: 14pt;">碰到函数进入执行。</span></div></li></ul><li><div><span style="font-size: 14pt;">左3：Stp Into My Code</span></div></li><ul><li><div><span style="font-size: 14pt;">在调用Step Into时会进入源代码，按该按钮会让程序再跳回当前代码位置。</span></div></li></ul><li><div><span style="font-size: 14pt;">左5：Step Out</span></div></li><ul><li><div><span style="font-size: 14pt;">执行完当前函数并调出该函数，跳到调用该函数的地方。</span></div></li></ul><li><div><span style="font-size: 14pt;">左6：Run to Cursor</span></div></li><ul><li><div><span style="font-size: 14pt;">执行到光标所在位置。</span></div></li></ul></ul></ul><div><font style="font-size: 14pt;"><br/></font></div><ul><li><div><span style="font-size: 14pt;">执行过程中查看打印输出的结果</span></div></li><ul><li><div><span style="font-size: 14pt;">点击图中所示Console界面，显示print 的结果。</span></div></li></ul></ul><div><span style="font-size: 14pt;"><img src="1-2 程序调试 - DEBUG功能应用_files/Image [4].png" type="image/png" data-filename="Image.png"/></span></div><div><font style="font-size: 14pt;"><br/></font></div><ul><li><div><span style="font-size: 14pt;">执行过程中添加监控变量</span></div></li><ul><li><div><span style="font-size: 14pt;">在代码中选择需要添加的变量，单击右键，选择Add to watches。</span></div></li></ul></ul><div><span style="font-size: 14pt;"><img src="1-2 程序调试 - DEBUG功能应用_files/Image [5].png" type="image/png" data-filename="Image.png"/></span></div><div><span style="font-size: 14pt;">监视器窗口，添加了新的监视变量：</span></div><div><span style="font-size: 14pt;"><img src="1-2 程序调试 - DEBUG功能应用_files/Image [6].png" type="image/png" data-filename="Image.png"/></span></div><div><font style="font-size: 14pt;"><br/></font></div><div><font style="font-size: 14pt;"><br/></font></div><hr/><div><font style="font-size: 14pt;"><br/></font></div><div><span style="color: rgb(105, 170, 53);">▶</span> <a href="0程序设计I-Python版- 实践1.html" style="color: rgb(105, 170, 53);">0程序设计I-Python版- 实践1</a></div><div><font style="font-size: 14pt;"><br/></font></div></div><div><br/></div></span>
</div></body></html> 